#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新T22_display.txt文档的脚本
根据U22_T22_mapping.txt中的映射关系，从u22_display.txt中找到对应的属性并更新到T22_display.txt中
"""

import re
import os
import sys

def read_mapping_file(mapping_file):
    """
    读取映射文件，返回TSMC到UMC的映射字典
    """
    mapping = {}
    try:
        with open(mapping_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    parts = line.split()
                    if len(parts) >= 2:
                        tsmc_name = parts[0]  # TSMC PacketName (如PPLUS)
                        umc_name = parts[1]   # UMC PacketName (如PP)
                        mapping[tsmc_name] = umc_name
                        print(f"映射关系: {tsmc_name} -> {umc_name}")
    except FileNotFoundError:
        print(f"错误: 找不到映射文件 {mapping_file}")
        return None
    except Exception as e:
        print(f"错误: 读取映射文件时出错 - {e}")
        return None
    
    return mapping

def parse_display_file(display_file):
    """
    解析display文件，提取PacketName定义
    返回字典: {PacketName: (完整行内容, 行号)}
    """
    packets = {}
    try:
        with open(display_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        for i, line in enumerate(lines):
            line = line.strip()
            # 匹配drDefinePacket中的PacketName定义
            # 格式: ( display PacketName Stipple LineStyle Fill Outline [FillStyle])
            match = re.match(r'\s*\(\s*display\s+(\w+)\s+(.+)\)', line)
            if match:
                packet_name = match.group(1)
                packet_definition = match.group(2)
                packets[packet_name] = (line, i + 1)
                print(f"找到PacketName: {packet_name} (行 {i + 1})")
                
    except FileNotFoundError:
        print(f"错误: 找不到文件 {display_file}")
        return None
    except Exception as e:
        print(f"错误: 读取文件 {display_file} 时出错 - {e}")
        return None
        
    return packets

def update_t22_display(t22_file, u22_packets, mapping):
    """
    更新T22_display.txt文件
    """
    try:
        # 读取T22文件
        with open(t22_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        updated_lines = []
        updates_made = 0
        
        for i, line in enumerate(lines):
            original_line = line
            line_stripped = line.strip()
            
            # 检查是否是PacketName定义行
            match = re.match(r'(\s*\(\s*display\s+)(\w+)(\s+.+\))', line_stripped)
            if match:
                prefix = match.group(1)
                packet_name = match.group(2)
                suffix = match.group(3)
                
                # 检查这个PacketName是否在映射表中
                if packet_name in mapping:
                    umc_packet_name = mapping[packet_name]
                    
                    # 在UMC文件中查找对应的PacketName
                    if umc_packet_name in u22_packets:
                        umc_line, umc_line_num = u22_packets[umc_packet_name]
                        
                        # 提取UMC的属性定义（除了PacketName之外的部分）
                        umc_match = re.match(r'\s*\(\s*display\s+\w+\s+(.+)\)', umc_line)
                        if umc_match:
                            umc_attributes = umc_match.group(1)
                            
                            # 构建新的行，保持原有的缩进和格式
                            indent = re.match(r'^(\s*)', original_line).group(1)
                            new_line = f"{indent}( display       {packet_name:<20} {umc_attributes}\n"
                            
                            updated_lines.append(new_line)
                            updates_made += 1
                            
                            print(f"更新 {packet_name}: 使用来自 {umc_packet_name} 的属性")
                            print(f"  原始: {line_stripped}")
                            print(f"  新的: {new_line.strip()}")
                            continue
                        else:
                            print(f"警告: 无法解析UMC文件中的 {umc_packet_name} 定义")
                    else:
                        print(f"警告: 在UMC文件中找不到 {umc_packet_name}")
            
            # 如果没有更新，保持原行
            updated_lines.append(original_line)
        
        # 写回文件
        if updates_made > 0:
            # 创建备份
            backup_file = t22_file + '.backup'
            with open(backup_file, 'w', encoding='utf-8') as f:
                with open(t22_file, 'r', encoding='utf-8') as original:
                    f.write(original.read())
            print(f"已创建备份文件: {backup_file}")
            
            # 写入更新后的内容
            with open(t22_file, 'w', encoding='utf-8') as f:
                f.writelines(updated_lines)
            
            print(f"成功更新 {updates_made} 个PacketName定义")
        else:
            print("没有找到需要更新的PacketName")
            
    except Exception as e:
        print(f"错误: 更新文件时出错 - {e}")
        return False
        
    return True

def main():
    """
    主函数
    """
    # 文件路径
    mapping_file = "U22_T22_mapping.txt"
    u22_file = "u22_display.txt"
    t22_file = "T22_display.txt"
    
    print("开始处理文件...")
    print(f"映射文件: {mapping_file}")
    print(f"UMC文件: {u22_file}")
    print(f"TSMC文件: {t22_file}")
    print("-" * 50)
    
    # 检查文件是否存在
    for file_path in [mapping_file, u22_file, t22_file]:
        if not os.path.exists(file_path):
            print(f"错误: 文件不存在 - {file_path}")
            return 1
    
    # 读取映射关系
    print("1. 读取映射关系...")
    mapping = read_mapping_file(mapping_file)
    if mapping is None:
        return 1
    
    if not mapping:
        print("警告: 映射文件为空")
        return 1
    
    print(f"找到 {len(mapping)} 个映射关系")
    print("-" * 50)
    
    # 解析UMC display文件
    print("2. 解析UMC display文件...")
    u22_packets = parse_display_file(u22_file)
    if u22_packets is None:
        return 1
    
    print(f"在UMC文件中找到 {len(u22_packets)} 个PacketName定义")
    print("-" * 50)
    
    # 更新TSMC文件
    print("3. 更新TSMC display文件...")
    success = update_t22_display(t22_file, u22_packets, mapping)
    
    if success:
        print("-" * 50)
        print("处理完成!")
    else:
        print("处理失败!")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
