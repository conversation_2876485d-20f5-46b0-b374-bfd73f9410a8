#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新T22_display.txt文档的脚本
根据U22_T22_mapping.txt中的映射关系，将u22_display.txt中的属性值更新到T22_display.txt中
"""

import re
import os


def read_mapping_file(mapping_file):
    """读取映射文件，返回UMC到TSMC的映射字典"""
    mapping = {}
    try:
        with open(mapping_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        # 跳过第一行标题
        for line in lines[1:]:
            line = line.strip()
            if line and '\t' in line:
                parts = line.split('\t')
                if len(parts) >= 2:
                    umc_name = parts[0].strip()
                    tsmc_name = parts[1].strip()
                    if umc_name and tsmc_name:
                        mapping[tsmc_name] = umc_name
                        
        print(f"成功读取映射关系 {len(mapping)} 条:")
        for tsmc, umc in mapping.items():
            print(f"  {tsmc} -> {umc}")
            
    except Exception as e:
        print(f"读取映射文件失败: {e}")
        
    return mapping


def parse_display_line(line):
    """解析display文件中的一行，提取层名和属性"""
    line = line.strip()
    
    # 匹配display或psb行的格式
    # 格式: ( display/psb LayerName attr1 attr2 attr3 attr4 attr5 )
    pattern = r'\(\s*(display|psb)\s+(\w+)\s+([^)]+)\)'
    match = re.match(pattern, line)
    
    if match:
        display_type = match.group(1)  # display 或 psb
        layer_name = match.group(2)
        attributes_str = match.group(3).strip()
        
        # 分割属性，处理可能的空格
        attributes = [attr.strip() for attr in attributes_str.split() if attr.strip()]
        
        return {
            'type': display_type,
            'layer': layer_name,
            'attributes': attributes,
            'original_line': line
        }
    
    return None


def read_display_file(display_file):
    """读取display文件，返回层名到属性的映射"""
    layers = {}
    
    try:
        with open(display_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        for i, line in enumerate(lines):
            parsed = parse_display_line(line)
            if parsed:
                layer_name = parsed['layer']
                if layer_name not in layers:
                    layers[layer_name] = {}
                layers[layer_name][parsed['type']] = {
                    'attributes': parsed['attributes'],
                    'line_number': i + 1,
                    'original_line': parsed['original_line']
                }
                
        print(f"从 {display_file} 读取到 {len(layers)} 个层定义")
        
    except Exception as e:
        print(f"读取display文件失败 {display_file}: {e}")
        
    return layers


def update_t22_display(t22_file, u22_layers, mapping):
    """更新T22_display.txt文件"""
    try:
        # 创建备份文件
        backup_file = t22_file + '.backup'
        import shutil
        shutil.copy2(t22_file, backup_file)
        print(f"已创建备份文件: {backup_file}")

        # 读取T22文件
        with open(t22_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        updated_lines = []
        updates_made = 0
        update_details = []

        for i, line in enumerate(lines):
            parsed = parse_display_line(line)

            if parsed:
                layer_name = parsed['layer']
                display_type = parsed['type']

                # 检查是否需要更新
                if layer_name in mapping and mapping[layer_name] in u22_layers:
                    umc_layer = mapping[layer_name]

                    # 检查u22中是否有对应的display/psb定义
                    if display_type in u22_layers[umc_layer]:
                        # 使用u22中的属性替换
                        u22_attributes = u22_layers[umc_layer][display_type]['attributes']

                        # 构建新的行，保持原有格式
                        new_line = f" ( {display_type}       {layer_name:<20} {' '.join(u22_attributes):<50} )\n"
                        updated_lines.append(new_line)

                        # 记录更新详情
                        update_info = {
                            'layer': layer_name,
                            'type': display_type,
                            'umc_layer': umc_layer,
                            'old_attributes': parsed['attributes'],
                            'new_attributes': u22_attributes,
                            'line_number': i + 1,
                            'changed': parsed['attributes'] != u22_attributes
                        }
                        update_details.append(update_info)
                        updates_made += 1
                    else:
                        # u22中没有对应的display/psb定义，保持原样
                        updated_lines.append(line)
                        print(f"警告: {layer_name} 在u22中没有找到对应的 {display_type} 定义")
                else:
                    # 不在映射中，保持原样
                    updated_lines.append(line)
            else:
                # 不是层定义行，保持原样
                updated_lines.append(line)

        # 显示更新详情
        if update_details:
            print("\n更新详情:")
            print("-" * 80)

            changed_count = 0
            unchanged_count = 0

            for detail in update_details:
                if detail['changed']:
                    print(f"✓ 已更新: {detail['layer']} ({detail['type']}) -> 映射到u22的 {detail['umc_layer']}")
                    print(f"  原属性: {' '.join(detail['old_attributes'])}")
                    print(f"  新属性: {' '.join(detail['new_attributes'])}")
                    print(f"  行号: {detail['line_number']}")
                    changed_count += 1
                else:
                    print(f"○ 无变化: {detail['layer']} ({detail['type']}) -> 映射到u22的 {detail['umc_layer']}")
                    print(f"  属性: {' '.join(detail['old_attributes'])}")
                    print(f"  行号: {detail['line_number']}")
                    unchanged_count += 1
                print("-" * 40)

            print(f"\n总结:")
            print(f"  实际更新: {changed_count} 个层")
            print(f"  无需更新: {unchanged_count} 个层")
            print(f"  总计处理: {len(update_details)} 个层")

        # 写回文件
        with open(t22_file, 'w', encoding='utf-8') as f:
            f.writelines(updated_lines)

        print(f"更新完成！共更新了 {updates_made} 个层定义")

    except Exception as e:
        print(f"更新T22文件失败: {e}")


def main():
    """主函数"""
    # 文件路径
    mapping_file = "U22_T22_mapping.txt"
    u22_display_file = "u22_display.txt"
    t22_display_file = "T22_display.txt"
    
    # 检查文件是否存在
    for file_path in [mapping_file, u22_display_file, t22_display_file]:
        if not os.path.exists(file_path):
            print(f"错误：文件不存在 {file_path}")
            return
            
    print("开始处理文件...")
    
    # 1. 读取映射关系
    print("\n1. 读取映射关系...")
    mapping = read_mapping_file(mapping_file)
    if not mapping:
        print("映射关系为空，退出")
        return
        
    # 2. 读取u22_display.txt
    print("\n2. 读取u22_display.txt...")
    u22_layers = read_display_file(u22_display_file)
    if not u22_layers:
        print("u22层定义为空，退出")
        return
        
    # 3. 更新T22_display.txt
    print("\n3. 更新T22_display.txt...")
    update_t22_display(t22_display_file, u22_layers, mapping)
    
    print("\n处理完成！")


if __name__ == "__main__":
    main()
